extends Control

# Notification system variables
@onready var notification_container: VBoxContainer = %notificationVBox
var active_notifications: Array[Control] = []

# Notification settings
const NOTIFICATION_WIDTH = 300
const NOTIFICATION_HEIGHT = 50
const APPEAR_DURATION = 0.25
const DISPLAY_DURATION = 10.0
const FADE_DURATION = 2.0
const NOTIFICATION_SPACING = 10

func _ready():
	setup_notification_system()
	connect_signals()

func setup_notification_system():
	# Create a VBoxContainer for stacking notifications in bottom-right
	#notification_container = VBoxContainer.new()
	notification_container.name = "NotificationContainer"

	# Position in bottom-right corner
	notification_container.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_RIGHT)
	notification_container.position.x -= NOTIFICATION_WIDTH + 20 # 20px margin from edge
	notification_container.position.y -= 20 # 20px margin from bottom

	# Add spacing between notifications
	notification_container.add_theme_constant_override("separation", NOTIFICATION_SPACING)

	# Add to scene
	#add_child(notification_container)

func connect_signals():
	# Connect to SignalBus for game events
	SignalBus.game_saved.connect(_on_game_saved)
	# Future: Add more signal connections here
	# SignalBus.game_loaded.connect(_on_game_loaded)
	# SignalBus.notification_requested.connect(_on_notification_requested)

# Signal handlers
func _on_game_saved():
	show_notification("res://assets/icons/saved.png", "Game Saved")

# Core notification system
func show_notification(icon_path: String, text: String):
	var unique_text = text + " " + str(randf() * 100)
	var notif_node = create_notification(icon_path, unique_text)

	# Add to container (new notifications go to the bottom)
	notification_container.add_child(notif_node)
	active_notifications.append(notif_node)

	# Start the notification lifecycle
	animate_notification_in(notif_node)

func create_notification(icon_path: String, text: String) -> Control:
	# Create main container
	var notif_node = HBoxContainer.new()
	notif_node.custom_minimum_size = Vector2(NOTIFICATION_WIDTH, NOTIFICATION_HEIGHT)
	notif_node.size_flags_horizontal = Control.SIZE_SHRINK_END

	# Create background panel for styling
	var background = Panel.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	notif_node.add_child(background)


	# Create label
	var label = Label.new()
	label.text = text
	label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	notif_node.add_child(label)

	# Create icon
	var icon = TextureRect.new()
	icon.custom_minimum_size = Vector2(50, 50)
	icon.expand_mode = TextureRect.EXPAND_IGNORE_SIZE
	icon.texture = load(icon_path)
	notif_node.add_child(icon)

	

	# Start below screen (for slide-up animation)
	#notif_node.position.y = NOTIFICATION_HEIGHT + NOTIFICATION_SPACING
	notif_node.modulate.a = 0.0

	return notif_node

# Animation functions
func animate_notification_in(notif_node: Control):
	# Create tween for slide-up animation
	var tween = create_tween()
	tween.set_parallel(true) # Allow multiple properties to animate simultaneously

	# Slide up from below screen
	tween.tween_property(notif_node, "position:y", 0, APPEAR_DURATION).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)

	# Fade in
	tween.tween_property(notif_node, "modulate:a", 1.0, APPEAR_DURATION).set_ease(Tween.EASE_OUT)

	# After appearing, wait for display duration then start fade out
	tween.tween_callback(start_notification_fadeout.bind(notif_node)).set_delay(APPEAR_DURATION + DISPLAY_DURATION)

func start_notification_fadeout(notif_node: Control):
	# Create tween for fade out
	var tween = create_tween()

	# Fade out
	tween.tween_property(notif_node, "modulate:a", 0.0, FADE_DURATION).set_ease(Tween.EASE_IN)

	# Remove notification after fade out
	tween.tween_callback(remove_notification.bind(notif_node))

func remove_notification(notif_node: Control):
	# Remove from active notifications array
	var index = active_notifications.find(notif_node)
	if index != -1:
		active_notifications.remove_at(index)

	# Remove from scene
	notif_node.queue_free()
