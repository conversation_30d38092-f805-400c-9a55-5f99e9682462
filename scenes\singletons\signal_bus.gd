extends Node

# ============================================================================
# SIGNAL BUS - Central hub for all game signals
# ============================================================================
# This autoload acts as the central communication hub for all game systems.
# Instead of connecting directly between systems, everything goes through here.
#
# ADDING NEW SIGNALS:
# 1. Declare the signal below in the appropriate category
# 2. Add an emit function if needed for external systems to trigger it
# 3. Connect to it from any scene that needs to react
#
# EXAMPLE - Adding a new signal:
# signal player_health_changed(new_health: int, max_health: int)
#
# func emit_player_health_changed(new_health: int, max_health: int):
#     player_health_changed.emit(new_health, max_health)
#
# EXAMPLE - Connecting to signals in a scene:
# func _ready():
#     SignalBus.time_changed.connect(_on_time_changed)
#     SignalBus.day_changed.connect(_on_day_changed)
#     SignalBus.game_saved.connect(_on_game_saved)
#
# func _on_time_changed(hours: int, minutes: int):
#     # Update UI clock display
#     clock_label.text = "%02d:%02d" % [hours, minutes]
#
# func _on_day_changed(new_day: int):
#     # Handle new day events
#     day_label.text = "Day " + str(new_day)
#     # Maybe trigger daily events, reset counters, etc.
#
# func _on_game_saved():
#     # Show save confirmation UI
#     show_notification("Game Saved!")
#
# EXAMPLE - Emitting signals from other systems:
# SignalBus.emit_game_saved()
# SignalBus.emit_menu_opened()
# SignalBus.emit_player_health_changed(75, 100)
# ============================================================================

# ============================================================================
# SIGNAL DECLARATIONS
# ============================================================================

# Time & Game State Signals
signal time_changed(hours: int, minutes: int)
signal day_changed(new_day: int)
signal game_paused()
signal game_resumed()

# Save/Load Signals
signal game_saved()
signal game_loaded()
signal save_failed(error_message: String)
signal load_failed(error_message: String)

# UI Signals
signal menu_opened(menu_name: String)
signal menu_closed(menu_name: String)
signal notification_requested(message: String, duration: float)

# Future expansion examples (commented out):
# signal player_health_changed(new_health: int, max_health: int)
# signal inventory_updated(item_name: String, quantity: int)
# signal research_completed(research_name: String)
# signal threat_level_changed(new_level: int)

# ============================================================================
# EMIT FUNCTIONS - For other systems to trigger signals
# ============================================================================

# Time & Game State
func emit_time_changed(hours: int, minutes: int):
	time_changed.emit(hours, minutes)

func emit_day_changed(new_day: int):
	day_changed.emit(new_day)

func emit_game_paused():
	game_paused.emit()

func emit_game_resumed():
	game_resumed.emit()

# Save/Load
func emit_game_saved():
	game_saved.emit()

func emit_game_loaded():
	game_loaded.emit()

func emit_save_failed(error_message: String):
	save_failed.emit(error_message)

func emit_load_failed(error_message: String):
	load_failed.emit(error_message)

# UI
func emit_menu_opened(menu_name: String):
	menu_opened.emit(menu_name)

func emit_menu_closed(menu_name: String):
	menu_closed.emit(menu_name)

func emit_notification_requested(message: String, duration: float = 3.0):
	notification_requested.emit(message, duration)
