; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Alien Invasion"
run/main_scene="res://scenes/MainScene.tscn"
config/features=PackedStringArray("4.3", "GL Compatibility")
config/icon="res://icon.svg"

[autoload]

GameMechanics="*res://scenes/singletons/game_mechanics.gd"
SignalBus="*res://scenes/singletons/signal_bus.gd"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/size/mode=3

[file_customization]

folder_colors={
"res://assets/": "red"
}

[rendering]

renderer/rendering_method="gl_compatibility"
renderer/rendering_method.mobile="gl_compatibility"
