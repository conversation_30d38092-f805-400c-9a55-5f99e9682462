extends Node

# Central signal bus for managing all game signals
# This consolidates signals from various systems for easier management


## Example Usage:
## Connect to time changes
SignalBus.time_changed.connect(_on_time_changed)
SignalBus.day_changed.connect(_on_day_changed)

# Connect to save/load events
SignalBus.game_saved.connect(_on_game_saved)
SignalBus.game_loaded.connect(_on_game_loaded)

func _on_time_changed(hours: int, minutes: int):
    # Update UI clock display
    clock_label.text = "%02d:%02d" % [hours, minutes]

func _on_day_changed(new_day: int):
    # Handle new day events
    print("Day ", new_day, " has begun!")

# From save/load system (already implemented)
SignalBus.emit_game_saved()
SignalBus.emit_game_loaded()

# From UI systems
SignalBus.emit_menu_opened()
SignalBus.emit_game_paused()

# Time-related signals (from GameMechanics)
signal time_changed(hours: int, minutes: int)
signal day_changed(new_day: int)

# Save/Load signals (for future use)
signal game_saved()
signal game_loaded()

# UI signals (for future use)
signal menu_opened()
signal menu_closed()
signal game_paused()
signal game_resumed()

func _ready():
    # Connect to GameMechanics signals when the signal bus is ready
    if GameMechanics:
        connect_game_mechanics_signals()
    else:
        # If GameMechanics isn't ready yet, wait for it
        call_deferred("connect_game_mechanics_signals")

func connect_game_mechanics_signals():
    # Connect GameMechanics signals to our signal bus
    if GameMechanics.time_changed.is_connected(_on_time_changed):
        return # Already connected

    GameMechanics.time_changed.connect(_on_time_changed)
    GameMechanics.day_changed.connect(_on_day_changed)

    print("SignalBus: Connected to GameMechanics signals")

# Signal relay functions - these receive signals from GameMechanics and re-emit them
func _on_time_changed(hours: int, minutes: int):
    time_changed.emit(hours, minutes)

func _on_day_changed(new_day: int):
    day_changed.emit(new_day)

# Utility functions for other systems to emit signals through the bus
func emit_game_saved():
    game_saved.emit()

func emit_game_loaded():
    game_loaded.emit()

func emit_menu_opened():
    menu_opened.emit()

func emit_menu_closed():
    menu_closed.emit()

func emit_game_paused():
    game_paused.emit()

func emit_game_resumed():
    game_resumed.emit()
